const utils = require("../utils/utils");
const dbQuery = require("../dbConfig/queryRunner");
const fcm = require("../utils/fcm");
const userService = require("./UsersService");
const utilsConstants = require("../utils/constants");
const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const moment = require("moment");
const CloudflareStreamService = require("./CloudflareStreamService");
// Get current time and add 5 hours 30 minute
const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
require('dotenv').config();

const bucketName = process.env.CLOUDFLARE_R3_BUCKET_NAME || "adtip";
const endpoint = process.env.CLOUDFLARE_R3_BUCKET_ENDPOINT;
const accessKeyId = process.env.CLOUDFLARE_R3_BUCKET_ACCESSKEYID;
const secretAccessKey = process.env.CLOUDFLARE_R3_BUCKET_SECRETACCESSKEY;
const region = "auto";

const cloudFlareFtpEndpoint = "https://theadtip.in";
const folderVideos = process.env.CLOUDFLARE_R3_BUCKET_FOLDER_VIDEOS || "videos";
const folderImages = process.env.CLOUDFLARE_R3_BUCKET_FOLDER_IMAGES || "images";

const generatePresignedUrl = async (data) => {
  // Log configuration for debugging
  console.log({
    bucketName,
    endpoint,
    accessKeyId: accessKeyId ? "****" : undefined, // Mask sensitive data
    secretAccessKey: secretAccessKey ? "****" : undefined, // Mask sensitive data
  });

  // Validate credentials
  if (!accessKeyId || !secretAccessKey || !endpoint) {
    throw new Error(
      `Missing required Cloudflare R2 configuration: accessKeyId=${!!accessKeyId}, secretAccessKey=${!!secretAccessKey}, endpoint=${!!endpoint}`
    );
  }

  const s3Client = new S3Client({
    endpoint,
    credentials: { accessKeyId, secretAccessKey },
    region,
    forcePathStyle: true, // Required for R2
  });

  const files = data.files || [];

  const results = await Promise.all(
    files.map(async (file) => {
      const { fileName, contentType } = file;
      const folder = contentType.startsWith("image/") ? folderImages : folderVideos;
      const key = `${folder}/${fileName}`;

      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        ContentType: contentType,
      });

      try {
        const presignedUrl = await getSignedUrl(s3Client, command, {
          expiresIn: 3600,
        });

        const publicUrl = `${cloudFlareFtpEndpoint}/${folder}/${fileName}`;

        return {
          fileName,
          presignedUrl,
          publicUrl,
          type: folder === folderImages ? "image" : "video",
        };
      } catch (error) {
        console.error(`Error generating presigned URL for ${fileName}:`, error);
        return {
          fileName,
          error: error.message || "Error generating presigned URL",
        };
      }
    })
  );

  return {
    status: 200,
    message: "Generated presigned URLs",
    data: results,
  };
};

// let generatePresignedUrl = (fileName) => {
//   const s3Client = new S3Client({
//     endpoint:
//       "https://02907c4bac2c44146c0fb36caaa1fe54.r2.cloudflarestorage.com", // Replace with your Cloudflare R2 endpoint
//     credentials: {
//       accessKeyId: "56022fde351ec25a42fc822038ea2a04",
//       secretAccessKey:
//         "****************************************************************",
//     },
//     region: "auto", // Adjust region as needed
//   });
//   // const s3 = new AWS.S3({
//   //   endpoint:
//   //     "https://02907c4bac2c44146c0fb36caaa1fe54.r2.cloudflarestorage.com",
//   //   accessKeyId: "56022fde351ec25a42fc822038ea2a04",
//   //   secretAccessKey:
//   //     "****************************************************************",
//   //   region: "auto",
//   //   signatureVersion: "v4",
//   // });
//   const command = new PutObjectCommand({
//     Bucket: "adtiphls",
//     Key: `${fileName}`,
//     ContentType: "image/jpg",
//   });

//   getSignedUrl(s3Client, command, { expiresIn: 60 })
//     .then((result) => {
//       return result;
//     })
//     .catch((error) => {
//       console.log("error", error);
//       return error;
//     });
//   // const params = {
//   //   Bucket: bucketName,
//   //   Key: key,
//   //   Expires: 60,
//   // };

//   // Generate the pre-signed URL
//   // return s3.getSignedUrlPromise("putObject", params);
// };

const uploadShot = (shotData) => {
  return new Promise((resolve, reject) => {
    // Parameterized query to prevent SQL injection
    const sql = `
      INSERT INTO reels (
        name, is_shot, category_id, video_channel, video_link, video_desciption,
        is_active, createdby, createddate, play_duration, video_Thumbnail,
        is_paid_promotional, promotional_price
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?)
    `;
    
    const values = [
      shotData.name,
      shotData.isShot,
      shotData.categoryId,
      shotData.channelId,
      shotData.videoLink,
      shotData.videoDesciption,
      1, // is_active
      shotData.createdby,
      shotData.play_duration,
      shotData.video_Thumbnail,
      shotData.is_paid_promotional !== undefined ? shotData.is_paid_promotional : false, // Default to false if not provided
      shotData.promotional_price !== undefined ? shotData.promotional_price : null // Default to null if not provided
    ];

    dbQuery
      .queryRunner(sql, values)
      .then((result) => {
        if (result && result.insertId) {
          shotData.id = result.insertId;

          // Generate QR Code
          const data = `${shotData.videoLink}`;
          const qrcodename = `user_${shotData.id}_qrcode.png`;
          utils.generateQRCode(data, qrcodename);
          dbQuery.queryRunner(
            `UPDATE reels SET qr_code_image = ? WHERE id = ?`,
            [qrcodename, shotData.id]
          );

          // Send notification
          fcm.sendNotificationToAll({
            title: utilsConstants.videoUploadTitle,
            body: `${shotData.name}`,
          });

          // Save notification
          userService.saveNotifications({
            title: `${utilsConstants.videoUploadTitle}`,
            subtitle: `${shotData.name}`,
            image: `${shotData.video_Thumbnail}`,
          });

          resolve({
            status: 200,
            message: "Shot added successfully.",
            data: [shotData],
          });
        } else {
          reject({
            status: 400,
            message: "Shot not saved.",
            data: [],
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid reference Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid reference Id.";
        reject({
          status: 500,
          message: message !== "" ? message : err.message,
          data: [],
        });
      });
  });
};

// Enhanced uploadShot with Cloudflare Stream integration
const uploadShotWithStream = async (shotData, videoFilePath = null) => {
  try {
    console.log('[ReelsService] Starting dual upload (R2 + Stream):', {
      name: shotData.name,
      hasVideoFile: !!videoFilePath
    });

    // 1. First, upload to R2 (existing functionality - keep as backup)
    const r2Result = await uploadShot(shotData);

    if (!r2Result || r2Result.status !== 200) {
      throw new Error('R2 upload failed');
    }

    const reelId = r2Result.data[0].id;
    console.log('[ReelsService] R2 upload successful, reel ID:', reelId);

    // 2. If video file path is provided, upload to Cloudflare Stream
    if (videoFilePath) {
      try {
        const streamResult = await CloudflareStreamService.uploadVideo(videoFilePath, {
          name: shotData.name,
          description: shotData.videoDesciption
        });

        if (streamResult.success) {
          // 3. Update database with Stream info
          const updateSql = `
            UPDATE reels SET
              stream_video_id = ?,
              stream_status = 'uploading',
              stream_created_at = NOW()
            WHERE id = ?
          `;

          await dbQuery.queryRunner(updateSql, [streamResult.uid, reelId]);

          console.log('[ReelsService] Stream upload initiated:', {
            reelId,
            streamId: streamResult.uid,
            status: streamResult.status
          });

          // Add stream info to response
          r2Result.data[0].stream_video_id = streamResult.uid;
          r2Result.data[0].stream_status = 'uploading';
        } else {
          console.warn('[ReelsService] Stream upload failed, continuing with R2 only:', streamResult.error);
        }
      } catch (streamError) {
        console.error('[ReelsService] Stream upload error, continuing with R2 only:', streamError);
        // Don't fail the entire upload if Stream fails - R2 backup is sufficient
      }
    }

    return r2Result;
  } catch (error) {
    console.error('[ReelsService] Dual upload error:', error);
    // Fallback to original R2-only upload
    return uploadShot(shotData);
  }
};

let saveChannelWithdrawRequest = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO channel_withdraw_request(channel_id,channel_name,withdraw_request_amount,created_by,created_time,channel_profile_image,total_ads_display,total_ads_view,total_ads_like,total_earnings,user_name,user_phone,status,upi_id) 
      VALUES(${data.channel_id},"${data.channel_name}",${data.withdraw_request_amount},${data.created_by},NOW(),"${data.channel_profile_image}",${data.total_ads_display},${data.total_ads_view},${data.total_ads_like},${data.total_earnings},"${data.user_name}","${data.user_phone}","Unpaid","${data.upi_id}")`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let sql1 = `UPDATE channels set total_earnings=total_earnings-${data.withdraw_request_amount} where id=${data.channel_id}`;
          dbQuery.queryRunner(sql1);
          resolve({
            status: 200,
            message: "Withdraw request added",
            data: [data],
          });
        } else {
          reject({
            status: 400,
            message: "Unable to send Withdraw request.",
            data: [],
          });
        }
      })
      .catch((err) => {
        console.log("error", err);
        reject({
          status: 400,
          message: `Unable to send Withdraw request. ${err}`,
          data: [],
        });
      });
  });
};

let getTransactions = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from channel_withdraw_request where channel_id=${data.channelId} and status="${data.status}" order by created_time desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Data found",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Data not found",
            data: [],
          });
        }
      })
      .catch((e) => {
        reject({
          status: 400,
          message: `Data not found ${e}`,
          data: [],
        });
      });
  });
};

let getShortById = (userId, reelId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT 
    r.id,r.name,r.category_id,r.video_link,r.video_desciption,r.total_views,r.total_likes,r.createdby,
    r.video_Thumbnail, 
    rdl.is_like, 
    cdf.is_follow as channel_follow, 
    c.name as channelName, 
    c.profile_image as channel_profile,
    c.id as channelId, 
    (SELECT COUNT(*) 
     FROM video_comments vc 
     WHERE vc.video_id = r.id) as total_comments
FROM 
    reels r  
LEFT JOIN 
    reel_details_like rdl ON rdl.reel_id = r.id AND rdl.user_id = ${userId}
LEFT JOIN 
    channels c ON r.video_channel = c.id  
LEFT JOIN 
    channel_details_followers cdf ON cdf.user_id = ${userId} AND cdf.channel_id = c.id
WHERE 
    r.id = ${reelId}
ORDER BY 
    r.createddate desc`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Shot fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Shot not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });
};

let channelShortsVideos = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT 
      r.id,r.name,r.category_id,r.video_link,r.video_desciption,r.total_views,r.total_likes,r.createdby,
      r.video_Thumbnail, 
      rdl.is_like, 
      cdf.is_follow as channel_follow, 
      c.name as channelName, 
      c.profile_image as channel_profile,
      c.id as channelId, 
      (SELECT COUNT(*) 
      FROM video_comments vc 
      WHERE vc.video_id = r.id) as total_comments
      FROM 
      reels r  
      LEFT JOIN 
      reel_details_like rdl ON rdl.reel_id = r.id AND rdl.user_id = ${userId}
      LEFT JOIN 
      channels c ON r.video_channel = c.id  
      LEFT JOIN 
      channel_details_followers cdf ON cdf.user_id = ${userId} AND cdf.channel_id = c.id
      WHERE 
      r.is_shot = 1 AND r.createdby=${userId}
      ORDER BY 
      r.createddate desc
    `;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Shot fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Shot not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });
};

const getShots = (userId) =>
  new Promise((resolve, reject) => {
    const sql = `SELECT
      r.id,r.name,r.category_id,r.video_link,r.video_desciption,r.total_views,r.total_likes,r.createdby,
      r.video_Thumbnail,
      r.stream_video_id, r.stream_status, r.adaptive_manifest_url, r.stream_ready_at,
      rdl.is_like,
      cdf.is_follow as channel_follow,
      c.name as channelName,
      c.profile_image as channel_profile,
      c.id as channelId,
      (SELECT COUNT(*)
      FROM video_comments vc
      WHERE vc.video_id = r.id) as total_comments
      FROM 
      reels r  
      LEFT JOIN 
      reel_details_like rdl ON rdl.reel_id = r.id AND rdl.user_id = ${userId}
      LEFT JOIN 
      channels c ON r.video_channel = c.id  
      LEFT JOIN 
      channel_details_followers cdf ON cdf.user_id = ${userId} AND cdf.channel_id = c.id
      WHERE
      r.is_shot = 1 AND r.video_link LIKE '%theadtip.in/%'
      ORDER BY
      CASE
        WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
        WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
        ELSE 3
      END,
      RAND(UNIX_TIMESTAMP() + r.id),
      r.total_likes DESC
      LIMIT 20
    `;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length !== 0) {
          resolve({
            status: 200,
            message: "Shot fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Shot not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

const getPublicShots = () =>
  new Promise((resolve, reject) => {
    const sql = `SELECT
      r.id,
      r.name,
      r.category_id,
      r.video_link,
      r.video_desciption AS video_description,
      r.total_views,
      r.total_likes,
      r.createdby,
      r.video_Thumbnail,
      r.stream_video_id,
      r.stream_status,
      r.adaptive_manifest_url,
      r.stream_ready_at,
      c.name AS channelName,
      c.profile_image AS channel_profile,
      c.id AS channelId,
      (SELECT COUNT(*)
       FROM video_comments vc
       WHERE vc.video_id = r.id) AS total_comments
    FROM 
      reels r  
    LEFT JOIN 
      channels c ON r.video_channel = c.id  
    WHERE
      r.is_shot = 1
      AND r.video_link LIKE '%theadtip.in/%'
    ORDER BY
      CASE
        WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
        WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
        ELSE 3
      END,
      RAND(UNIX_TIMESTAMP() + r.id),
      r.total_views DESC
    LIMIT 20`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length > 0) {
          resolve({
            status: 200,
            message: "Shots fetched successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "No shots found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  });  

let getVideoCatagory = () =>
  new Promise((resolve, reject) => {
    const sql = `select id, category_name as categoryName from video_master where is_active=1 ORDER BY order_by ASC;`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Video Catagory fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Video Catagory not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveVideoLike = async (reelData) => {
  try {
    // Validate input data
    if (!reelData.reelId || !reelData.userId || reelData.like === undefined || !reelData.reelCreatorId) {
      return {
        status: 400,
        message: "Missing required fields: reelId, userId, like, reelCreatorId",
        data: [],
      };
    }

    // Check if user has already interacted with this reel
    const checkSql = `SELECT * FROM reel_details_like WHERE reel_id=${reelData.reelId} AND user_id=${reelData.userId}`;
    const existingRecord = await dbQuery.queryRunner(checkSql);

    if (!existingRecord || existingRecord.length === 0) {
      // No existing record - insert new like/dislike
      if (reelData.like === 0) {
        // Insert dislike
        await dbQuery.queryRunner(
          `INSERT INTO reel_details_like (reel_id,user_id,is_like,reel_creator,created_date,updated_date)
          VALUES(${reelData.reelId},${reelData.userId},0,${reelData.reelCreatorId},NOW(),NOW())`
        );
        await dbQuery.queryRunner(
          `UPDATE reels SET total_likes = total_likes - 1 WHERE id=${reelData.reelId}`
        );
        return {
          status: 200,
          message: "Dislike saved.",
          data: [reelData],
        };
      } else if (reelData.like === 1) {
        // Insert like
        await dbQuery.queryRunner(
          `INSERT INTO reel_details_like (reel_id,user_id,is_like,reel_creator,created_date,updated_date)
          VALUES(${reelData.reelId},${reelData.userId},1,${reelData.reelCreatorId},NOW(),NOW())`
        );
        await dbQuery.queryRunner(
          `UPDATE reels SET total_likes = total_likes + 1 WHERE id=${reelData.reelId}`
        );
        return {
          status: 200,
          message: "Like saved.",
          data: [reelData],
        };
      } else {
        return {
          status: 400,
          message: "Invalid like value. Must be 0 or 1.",
          data: [reelData],
        };
      }
    } else {
      // Existing record found - update based on current state and new action
      const currentLikeStatus = existingRecord[0].is_like;

      if (currentLikeStatus === 0 && reelData.like === 0) {
        return {
          status: 200,
          message: "Already disliked",
          data: [reelData],
        };
      } else if (currentLikeStatus === 0 && reelData.like === 1) {
        // Change from dislike to like
        await dbQuery.queryRunner(
          `UPDATE reel_details_like SET is_like=1, updated_date=NOW() WHERE reel_id=${reelData.reelId} AND user_id=${reelData.userId}`
        );
        await dbQuery.queryRunner(
          `UPDATE reels SET total_likes = total_likes + 2 WHERE id=${reelData.reelId}`
        ); // +2 because we're removing dislike (-1) and adding like (+1)
        return {
          status: 200,
          message: "Changed from dislike to like.",
          data: [reelData],
        };
      } else if (currentLikeStatus === 1 && reelData.like === 0) {
        // Change from like to dislike
        await dbQuery.queryRunner(
          `UPDATE reel_details_like SET is_like=0, updated_date=NOW() WHERE reel_id=${reelData.reelId} AND user_id=${reelData.userId}`
        );
        await dbQuery.queryRunner(
          `UPDATE reels SET total_likes = total_likes - 2 WHERE id=${reelData.reelId}`
        ); // -2 because we're removing like (-1) and adding dislike (-1)
        return {
          status: 200,
          message: "Changed from like to dislike.",
          data: [reelData],
        };
      } else if (currentLikeStatus === 1 && reelData.like === 1) {
        return {
          status: 200,
          message: "Already liked",
          data: [reelData],
        };
      } else if (currentLikeStatus === null && reelData.like === 0) {
        // Change from neutral to dislike
        await dbQuery.queryRunner(
          `UPDATE reel_details_like SET is_like=0, updated_date=NOW() WHERE reel_id=${reelData.reelId} AND user_id=${reelData.userId}`
        );
        await dbQuery.queryRunner(
          `UPDATE reels SET total_likes = total_likes - 1 WHERE id=${reelData.reelId}`
        );
        return {
          status: 200,
          message: "Dislike saved.",
          data: [reelData],
        };
      } else if (currentLikeStatus === null && reelData.like === 1) {
        // Change from neutral to like
        await dbQuery.queryRunner(
          `UPDATE reel_details_like SET is_like=1, updated_date=NOW() WHERE reel_id=${reelData.reelId} AND user_id=${reelData.userId}`
        );
        await dbQuery.queryRunner(
          `UPDATE reels SET total_likes = total_likes + 1 WHERE id=${reelData.reelId}`
        );
        return {
          status: 200,
          message: "Like saved.",
          data: [reelData],
        };
      } else {
        return {
          status: 400,
          message: "Invalid like operation.",
          data: [reelData],
        };
      }
    }
  } catch (error) {
    console.error("Error in saveVideoLike:", error);
    return {
      status: 500,
      message: `Database error: ${error.message}`,
      data: [],
    };
  }
};

let saveVideoDetails = (reelsData) =>
  new Promise(async (resolve, reject) => {
    try {
      // Check if the user has already viewed the video
      if (reelsData.isView === 1) {
        let checkViewSql = `SELECT COUNT(*) as count FROM reels_details WHERE reel_id=${reelsData.reelId} AND user_id=${reelsData.userId} AND is_view=1`;
        let checkViewResult = await dbQuery.queryRunner(checkViewSql);

        if (checkViewResult[0].count > 0) {
          return reject({
            status: 400,
            message: "User has already viewed this video.",
            data: [],
          });
        }
      }

      let sql = `INSERT INTO reels_details (reel_id, user_id, played_duration, is_like, is_view, is_watch_later, createdby, createddate, updateddate)
        VALUES (${reelsData.reelId}, ${reelsData.userId}, 
        ${reelsData.playedDuration ? `'${reelsData.playedDuration}'` : "NULL"}, 
        ${reelsData.isLike !== undefined ? reelsData.isLike : "NULL"},
        ${reelsData.isView !== undefined ? reelsData.isView : "NULL"},
        ${
          reelsData.isWatchLater !== undefined ? reelsData.isWatchLater : "NULL"
        },
        ${reelsData.createdby}, NOW(), NOW()) 
        ON DUPLICATE KEY UPDATE 
        played_duration = VALUES(played_duration),
        is_like = VALUES(is_like),
        is_view = VALUES(is_view),
        is_watch_later = VALUES(is_watch_later),
        updateddate = NOW();`;

      console.log("Executing Query:", sql);

      let result = await dbQuery.queryRunner(sql);

      // Handle Likes
      if (reelsData.isLike !== undefined) {
        let likeQuery =
          reelsData.isLike === 1
            ? `UPDATE reels SET total_likes = total_likes + 1 WHERE id=${reelsData.reelId};`
            : `UPDATE reels SET total_likes = total_likes - 1 WHERE id=${reelsData.reelId};`;

        await dbQuery.queryRunner(likeQuery);
      }

      // Handle Views
      if (reelsData.isView === 1) {
        let viewQuery = `UPDATE reels SET total_views = total_views + 1 WHERE id=${reelsData.reelId};`;
        await dbQuery.queryRunner(viewQuery);
      }

      resolve({
        status: 200,
        message: "Video data saved successfully.",
        data: [reelsData],
      });
    } catch (err) {
      let message = "";
      if (err.message.includes("ER_DUP_ENTRY"))
        message = "Ad already available.";
      if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
        message = "Invalid reference Id.";
      if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
        message = "Invalid reference Id.";

      reject({
        status: 500,
        message: message !== "" ? message : err.message,
        data: [],
      });
    }
  });

let getUserVideoViewLikeDetails = (userId, page) => {
  var offset = page * 20;
  return new Promise((resolve, reject) => {
    let sql = `SELECT c.id as channedlId, rd.id as watch_id,r.* from reels_details rd left join reels r on r.id=rd.reel_id left join channels c on r.video_channel=c.id where user_id=${userId} and r.is_shot=0 order by createddate desc limit 20 offset ${offset}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "data found",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "data not found",
            data: [],
          });
        }
      })
      .catch((e) => {
        reject({
          status: 400,
          message: `Error occurred ${e}`,
          data: [],
        });
      });
  });
};

let saveChannelFollowers = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * FROM channel_details_followers where user_id=${data.userId} and channel_id=${data.channelId}`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        ///1. is_follow=0
        ///2. is_follow=1
        var isFollow = result[0].is_follow;
        console.log("isFollow", isFollow);
        if (data.follow === 0 && isFollow === 0) {
          resolve({
            status: 200,
            message: "You alreday unfollowed this channel earlier",
            data: [data],
          });
        } else if (data.follow === 0 && isFollow === 1) {
          dbQuery.queryRunner(
            `UPDATE channel_details_followers set is_follow=0, updated_date=NOW() where user_id=${data.userId} and channel_id=${data.channelId}`
          );
          dbQuery.queryRunner(
            `UPDATE channels set total_followers=total_followers-1 where id=${data.channelId}`
          );

          resolve({
            status: 200,
            message: "followed before now unfollowed",
            data: [data],
          });
        } else if (data.follow === 1 && isFollow === 0) {
          dbQuery.queryRunner(
            `UPDATE channel_details_followers set is_follow=1, updated_date=NOW() where user_id=${data.userId} and channel_id=${data.channelId}`
          );
          dbQuery.queryRunner(
            `UPDATE channels set total_followers=total_followers+1 where id=${data.channelId}`
          );

          resolve({
            status: 200,
            message: "unfollowed before now followed",
            data: [data],
          });
        } else {
          resolve({
            status: 200,
            message: "You alreday followed",
            data: [data],
          });
        }
      } else {
        ///insert into db
        dbQuery.queryRunner(
          `INSERT INTO channel_details_followers (channel_id,user_id,is_follow,created_date,updated_date) VALUES(${data.channelId},${data.userId},1,NOW(),NOW())`
        );
        dbQuery.queryRunner(
          `UPDATE channels set total_followers=total_followers+1 where id=${data.channelId}`
        );
        resolve({
          status: 200,
          message: "Followed successfully",
          data: [data],
        });
      }
    });
  }).catch((e) => {
    reject({
      status: 400,
      message: `Error occurred ${e}`,
      data: [],
    });
  });
};

let saveChannelDetails = (channelModel) =>
  new Promise((resolve, reject) => {
    let sql = "";

    if (channelModel.isLike)
      sql += `INSERT INTO channel_details (channel_id,user_id,is_like,createdby,createddate) VALUES(${channelModel.channelId},${channelModel.userId},${channelModel.isLike},${channelModel.createdby},NOW()) ON DUPLICATE KEY UPDATE`;
    if (channelModel.isView)
      sql += `INSERT INTO channel_details (channel_id,user_id,is_view,createdby,createddate) VALUES(${channelModel.channelId},${channelModel.userId},'${channelModel.isView}',${channelModel.createdby},NOW()) ON DUPLICATE KEY UPDATE`;
    if (channelModel.isFollow)
      sql += `INSERT INTO channel_details (channel_id,user_id,is_follow,createdby,createddate) VALUES(${channelModel.channelId},${channelModel.userId},'${channelModel.isFollow}',${channelModel.createdby},NOW()) ON DUPLICATE KEY UPDATE`;

    // if (channelModel.playedDuration) sql += ` played_duration = '${channelModel.playedDuration ? channelModel.playedDuration : null}'`;
    let lastWord = sql.split(" ").pop();

    //if (lastWord && lastWord != 'UPDATE' && channelModel.playedDuration) sql += ` ,`;
    //lastWord = sql.split(' ').pop();

    if (channelModel.isLike) {
      sql += ` is_like = ${channelModel.isLike ? channelModel.isLike : null}`;
      lastWord = sql.split(" ").pop();

      if (channelModel.isLike === "1")
        dbQuery.queryRunner(
          `update channels set total_likes = total_likes + 1 where id=${channelModel.channelId};`
        );
      if (channelModel.isLike === "0")
        dbQuery.queryRunner(
          `update channels set total_likes = total_likes - 1 where id=${channelModel.channelId};`
        );
    }

    if (lastWord && lastWord != "UPDATE" && channelModel.isLike) sql += ` ,`;
    lastWord = sql.split(" ").pop();

    if (channelModel.isView) {
      sql += ` is_view = ${channelModel.isView ? channelModel.isView : null}`;
      lastWord = sql.split(" ").pop();
      if (channelModel.isView === "1")
        dbQuery.queryRunner(
          `update channels set total_views = total_views + 1 where id=${channelModel.channelId};`
        );
      if (channelModel.isView === "0")
        dbQuery.queryRunner(
          `update channels set total_views = total_views - 1 where id=${channelModel.channelId};`
        );
    }

    if (lastWord && lastWord != "UPDATE" && channelModel.isView) sql += ` ,`;
    lastWord = sql.split(" ").pop();

    if (channelModel.isFollow) {
      sql += ` is_follow= ${
        channelModel.isFollow ? channelModel.isFollow : null
      }`;
      lastWord = sql.split(" ").pop();
      if (channelModel.isFollow === "1")
        dbQuery.queryRunner(
          `update channels set total_followers = total_followers + 1 where id=${channelModel.channelId};`
        );
      if (channelModel.isFollow === "0")
        dbQuery.queryRunner(
          `update channels set total_followers = total_followers - 1 where id=${channelModel.channelId};`
        );
    }

    if (lastWord && lastWord != "UPDATE" && channelModel.isFollow) sql += ` ,`;
    lastWord = sql.split(" ").pop();

    sql += ` updateddate = NOW();`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Channel Updated.",
            data: [channelModel],
          });
        } else {
          reject({
            status: 400,
            message: "Channel not created.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let saveMyChannel = (channelModel) =>
  new Promise(async (resolve, reject) => {
    try {
      // Check if the user already has a channel
      const checkSql = `SELECT id FROM channels WHERE createdby = ${channelModel.createdBy} LIMIT 1`;
      const existingChannel = await dbQuery.queryRunner(checkSql);

      if (existingChannel.length > 0) {
        return reject({
          status: 400,
          message: "User already has a channel.",
          data: [],
        });
      }

      // If no existing channel, proceed with insertion
      let channelURL = 1;
      let sql = `INSERT INTO channels(name, description, channel_url, profile_image, profile_cover_image,
          total_subscribers, total_videos, total_shorts, is_active, createdby,
          updatedby, createddate, updateddate)
      VALUES ('${channelModel.channelName}', '${channelModel.channelDescription}', '${channelURL}',
      '${channelModel.profileImageURL}', '${channelModel.coverImageURL}', 0, 0, 0, 1,
      ${channelModel.createdBy}, ${channelModel.updatedBy}, NOW(), NOW());`;

      const result = await dbQuery.queryRunner(sql);

      if (result && result.affectedRows > 0) {
        resolve({
          status: 200,
          message: "Channel created successfully.",
          data: [channelModel],
        });
      } else {
        reject({
          status: 400,
          message: "Channel creation failed.",
          data: [],
        });
      }
    } catch (err) {
      let message = "Something went wrong. Please try again.";
      if (err.message.includes("ER_DUP_ENTRY"))
        message = "Channel already exists.";
      if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
        message = "Invalid reference ID.";
      if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
        message = "Invalid reference ID.";

      reject({
        status: 500,
        message,
        data: [],
      });
    }
  });

let getWatchingVideo = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select r.*,rd.played_duration,rd.is_like, rd.is_view,0 as channel_follow,(SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=r.createdby
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 and rd.user_id=${userId} and rd.played_duration IS NOT NULL order by r.createddate desc`;

    let sql1 = `select channel_id as channelId,is_follow as isFollow from channel_details where user_id=${userId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAllVideos = (userId, categoryId, page) =>
  new Promise(async (resolve, reject) => {
    let offset = (page - 1) * 20;

    let sql = `select r.*,rd.is_like, rd.is_view as is_view, 0 as channel_follow,(SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 and ad_model_id=2 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
        c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
        c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId,
        (SELECT content_creator_plan_id FROM users WHERE id = r.createdby) as content_creator_plan_id
        from reels r  
        left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=r.createdby
        left JOIN channels c ON r.video_channel=c.id
        where r.is_shot=0 AND r.video_link LIKE '%theadtip.in/%'`;

    if (categoryId != 0) sql += ` and r.category_id=${categoryId}`;
    sql += ` ORDER BY
      CASE
        WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
        WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
        ELSE 3
      END,
      RAND(UNIX_TIMESTAMP() + r.id + ${userId}),
      r.total_views DESC,
      r.createddate DESC
      LIMIT 20 OFFSET ${offset}`;

    let sql1 = `select is_follow as channel_follow, channel_id as channelId from channel_details where user_id=${userId}`;

    try {
      const [videos, channelFollows] = await Promise.all([
        dbQuery.queryRunner(sql),
        dbQuery.queryRunner(sql1)
      ]);
      if (videos && videos.length > 0) {
        // For each video, check if the owner has an active content creator subscription
        for (const video of videos) {
          // Default to 0
          video.has_content_creator_premium = 0;
          const subscriptionQuery = `
            SELECT status, current_end_at
            FROM content_creator_subscriptions
            WHERE user_id = ? AND status = 'active'
            ORDER BY created_at DESC
            LIMIT 1
          `;
          const subscription = await dbQuery.queryRunner(subscriptionQuery, [video.createdby]);
          if (subscription.length && subscription[0].current_end_at && new Date(subscription[0].current_end_at) > new Date()) {
            video.has_content_creator_premium = 1;
          }
        }
        if (channelFollows && channelFollows.length > 0) {
          videos.forEach((reelsData) => {
            channelFollows.forEach((data) => {
              reelsData.is_unlike =
                reelsData.is_like == 1
                  ? 0
                  : reelsData.is_like == null
                  ? null
                  : reelsData.is_like;
              if (data.channelId == reelsData.video_channel) {
                reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
              }
            });
          });
        }
        resolve({
          status: 200,
          message: "Video fetch successfully.",
          data: videos,
        });
      } else {
        resolve({
          status: 200,
          message: "Video not found.",
          data: [],
        });
      }
    } catch (err) {
      reject({
        status: 500,
        message: err.message,
        data: [],
      });
    }
  });

let getPublicVideos = (categoryId, page) =>
  new Promise((resolve, reject) => {
    let offset = (page - 1) * 20;

    let sql = `SELECT r.*,
      (SELECT ad_upload_filename FROM admodels WHERE FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) OR FIND_IN_SET('all', LOWER(ad_place_app)) AND is_active=1 AND ad_model_id=2 ORDER BY RAND() LIMIT 1) AS adUrl,
      (SELECT id FROM admodels WHERE FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) OR FIND_IN_SET('all', LOWER(ad_place_app)) AND is_active=1 ORDER BY RAND() LIMIT 1) AS adId,
      c.total_likes AS total_channel_likes,
      c.total_followers AS total_channel_followers,
      c.total_videos AS total_channel_video,
      c.name AS channelName,
      c.profile_image AS channel_profile,
      c.id AS channelId,
      c.profile_cover_image AS channelProfileCover,
      c.total_shorts AS totalChannelShots,
      c.total_views AS totalChannelViews,
      (SELECT content_creator_plan_id FROM users WHERE id = r.createdby) AS content_creator_plan_id
      FROM reels r
      LEFT JOIN channels c ON r.video_channel = c.id
      WHERE r.is_shot = 0 AND r.video_link LIKE '%theadtip.in/%'`;

    if (categoryId != 0) sql += ` AND r.category_id = ${categoryId}`;
    sql += ` ORDER BY r.createddate DESC LIMIT 20 OFFSET ${offset}`;

    dbQuery.queryRunner(sql)
      .then((result) => {
        if (result && result.length > 0) {
          resolve({
            status: 200,
            message: "Videos fetched successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "No videos found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  });  

let getVideoDetails = (userId, videoId) =>
  new Promise((resolve, reject) => {
    // Validate inputs
    const userIdNum = parseInt(userId) || 0;
    const videoIdNum = parseInt(videoId);

    if (!videoIdNum || isNaN(videoIdNum)) {
      reject({
        status: 400,
        message: "Invalid video ID",
        data: [],
      });
      return;
    }

    console.log('[getVideoDetails] Processing request for userId:', userIdNum, 'videoId:', videoIdNum);

    let sql = `select r.*,
    (select is_like from reel_details_like where reel_id=? and user_id=?) as is_like,
    (select is_view from reels_details where reel_id=? and user_id=?) as is_view,
    (select IFNULL(is_follow,0) as channel_follow from channel_details_followers where user_id=? and channel_id = (select video_channel from reels where id=?)) as channel_follow,
    (SELECT ad_upload_filename FROM admodels where ad_upload_filename is not null AND  is_active=1 and ad_model_id=2 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where ad_upload_filename is not null AND is_active=1 ORDER BY RAND() LIMIT 1) as adId,
    c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
  c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
  from reels r left JOIN channels c ON r.video_channel=c.id
  where r.id=? order by r.createddate desc`;

    let sql1 = `SELECT adv.ad_upload_filename as adUrl,adv.id as adId,adv.ad_website,adv.ad_model_id,adv.company_id,adv.company_name,adv.mediaType,adm.name,adm.model_image,adm.is_brand FROM admodels adv
  LEFT JOIN admodels_master adm
  ON adv.ad_model_id=adm.id
  where adv.ad_upload_filename is not null AND adv.is_active=1 AND (adv.ad_model_id=2 OR adv.ad_model_id=30) ORDER BY RAND() LIMIT 1;`;

    Promise.all([
      dbQuery.queryRunner(sql, [videoIdNum, userIdNum, videoIdNum, userIdNum, userIdNum, videoIdNum, videoIdNum]),
      dbQuery.queryRunner(sql1)
    ])
      .then((result) => {
        console.log('[getVideoDetails] Query results:', result);
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
            adDetails: result[1],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        console.error('[getVideoDetails] Database error:', err);
        reject({
          status: 500,
          message: err.message || "Database query failed",
          data: [],
        });
      });
  });

let editVideo = (video) => {
  return new Promise((resolve, reject) => {
    // Validate required fields
    if (!video.id || !video.userId) {
      return reject({
        status: 400,
        message: "Video ID and User ID are required",
        data: [],
      });
    }

    // First check if the video exists and belongs to the user
    const checkOwnershipSql = `SELECT id, createdby FROM reels WHERE id = ? AND createdby = ?`;

    dbQuery
      .queryRunner(checkOwnershipSql, [video.id, video.userId])
      .then((ownershipResult) => {
        if (!ownershipResult || ownershipResult.length === 0) {
          return reject({
            status: 403,
            message: "Video not found or you don't have permission to edit this video",
            data: [],
          });
        }

        // Build dynamic update query based on provided fields
        let updateFields = [];
        let updateValues = [];

        if (video.name !== undefined) {
          updateFields.push('name = ?');
          updateValues.push(video.name);
        }
        if (video.thumbnail !== undefined) {
          updateFields.push('video_Thumbnail = ?');
          updateValues.push(video.thumbnail);
        }
        if (video.description !== undefined) {
          updateFields.push('video_desciption = ?');
          updateValues.push(video.description);
        }

        if (updateFields.length === 0) {
          return reject({
            status: 400,
            message: "No fields to update",
            data: [],
          });
        }

        // Add updated timestamp and video ID for WHERE clause
        updateFields.push('updateddate = NOW()');
        updateValues.push(video.id);

        const updateSql = `UPDATE reels SET ${updateFields.join(', ')} WHERE id = ?`;

        return dbQuery.queryRunner(updateSql, updateValues);
      })
      .then((result) => {
        if (result && result.affectedRows > 0) {
          resolve({
            status: 200,
            message: "Video edited successfully",
            data: [video],
          });
        } else {
          resolve({
            status: 200,
            message: "No changes made to video",
            data: [video],
          });
        }
      })
      .catch((e) => {
        console.error('Error editing video:', e);
        reject({
          status: 500,
          message: `Unable to edit video: ${e.message || e}`,
          data: [],
        });
      });
  });
};

let deleteVideo = (videoId, userId) => {
  return new Promise((resolve, reject) => {
    // Validate required parameters
    if (!videoId || !userId) {
      return reject({
        status: 400,
        message: "Video ID and User ID are required",
        data: [],
      });
    }

    // First check if the video exists and belongs to the user
    const checkOwnershipSql = `SELECT id, createdby, video_link FROM reels WHERE id = ? AND createdby = ?`;

    dbQuery
      .queryRunner(checkOwnershipSql, [videoId, userId])
      .then((ownershipResult) => {
        if (!ownershipResult || ownershipResult.length === 0) {
          return reject({
            status: 403,
            message: "Video not found or you don't have permission to delete this video",
            data: [],
          });
        }

        // Soft delete by setting is_active = 0 instead of hard delete
        // This preserves data integrity and allows for recovery if needed
        const softDeleteSql = `UPDATE reels SET is_active = 0, updateddate = NOW() WHERE id = ? AND createdby = ?`;

        return dbQuery.queryRunner(softDeleteSql, [videoId, userId]);
      })
      .then((result) => {
        if (result && result.affectedRows > 0) {
          console.log("Video soft deleted successfully:", { videoId, userId });
          resolve({
            status: 200,
            message: "Video deleted successfully",
            data: [],
          });
        } else {
          reject({
            status: 500,
            message: "Unable to delete video",
            data: [],
          });
        }
      })
      .catch((e) => {
        console.error('Error deleting video:', e);
        reject({
          status: 500,
          message: `Unable to delete video: ${e.message || e}`,
          data: [],
        });
      });
  });
};

let getVideoByChannel = (userId, channedlId, videoType) =>
  new Promise((resolve, reject) => {
    //   let sql = `select r.*, 0 as is_like, 0 as is_view,
    //   (select IFNULL(is_follow,0) as channel_follow from channel_details where user_id=${userId} and channel_id = ${channedlId} ) as channel_follow,
    //   (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    //   (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId,
    // c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
    // c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
    // from reels r left JOIN channels c ON r.video_channel=c.id
    // where r.video_channel=${channedlId} AND r.is_shot= ${videoType} order by r.createddate desc`;
    let sql = `
    (
      SELECT r.*, 0 as is_like, 0 as is_view,
        (SELECT IFNULL(is_follow, 0) as channel_follow FROM channel_details WHERE user_id=${userId} AND channel_id=r.video_channel) as channel_follow,
        (SELECT ad_upload_filename FROM admodels WHERE FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) OR FIND_IN_SET('all', LOWER(ad_place_app)) AND is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
        (SELECT id FROM admodels WHERE FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) OR FIND_IN_SET('all', LOWER(ad_place_app)) AND is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
        c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
        c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
      FROM reels r 
      LEFT JOIN channels c ON r.video_channel = c.id
      WHERE r.video_channel = ${channedlId} AND r.is_shot = ${videoType} AND r.is_active=1
      ORDER BY r.createddate DESC
    )
  `;

    /*
          UNION ALL
    (
      SELECT r.*, 0 as is_like, 0 as is_view,
        (SELECT IFNULL(is_follow, 0) as channel_follow FROM channel_details WHERE user_id=${userId} AND channel_id=r.video_channel) as channel_follow,
        (SELECT ad_upload_filename FROM admodels WHERE FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) OR FIND_IN_SET('all', LOWER(ad_place_app)) AND is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
        (SELECT id FROM admodels WHERE FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) OR FIND_IN_SET('all', LOWER(ad_place_app)) AND is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
        c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
        c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
      FROM reels r 
      LEFT JOIN channels c ON r.video_channel = c.id
      WHERE r.video_channel != ${channedlId} AND r.is_shot = ${videoType}
      ORDER BY r.createddate DESC
      LIMIT 100
    )
     

    */

    console.log("get video by channel sql query\n", sql);
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getCommentOfVideo = (videoId, page, limit) => {
  return new Promise((resolve, reject) => {
    let offset = page * limit;
    let sql = `SELECT * from video_comments where video_id= ${videoId} order by createddate desc  LIMIT ${limit} OFFSET ${offset}`;
    let sql1 = `SELECT COUNT(*) as total from video_comments where video_id= ${videoId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].total = result[1][0];
            resolve({
              status: 200,
              message: "Comment fetch successfully.",
              total: result[1][0].total,
              data: result[0],
            });
          }

          resolve({
            status: 200,
            total: result[1][0].total,
            message: "Comment fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            total: result[1][0].total,
            message: "Comment not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          total: 0,
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });
};

let getcommentsofvideos = (userId, videoId) =>
  new Promise((resolve, reject) => {
    let sql = `
      SELECT vd.*, 
        c.id AS channelId, 
        c.name AS channelName, 
        c.total_likes AS total_channel_likes, 
        c.total_followers AS total_channel_followers, 
        c.total_videos AS total_channel_video, 
        c.profile_image AS channel_profile, 
        c.profile_cover_image AS channelProfileCover 
      FROM video_comments vd
      LEFT JOIN reels r ON r.id = vd.video_id
      LEFT JOIN channels c ON c.createdby = vd.createdby
      WHERE vd.video_id = ? AND vd.is_active = 1
      ORDER BY vd.createddate DESC
    `;

    let sql1 = `SELECT * FROM comment_details WHERE user_id = ?`;

    Promise.all([
      dbQuery.queryRunner(sql, [videoId]),
      dbQuery.queryRunner(sql1, [userId]),
    ])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((videoComment) => {
              result[1].forEach((commentDetails) => {
                if (commentDetails.comment_id === videoComment.id) {
                  videoComment.is_unlike = commentDetails.is_unlike
                    ? commentDetails.is_unlike
                    : 0;
                  videoComment.is_like = commentDetails.is_like
                    ? commentDetails.is_like
                    : 0;
                  videoComment.is_react = commentDetails.is_react
                    ? commentDetails.is_react
                    : 0;
                  videoComment.react_value = commentDetails.react_value
                    ? commentDetails.react_value
                    : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Comment fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Comment not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let deletecommentbyid = (commentId) =>
  new Promise((resolve, reject) => {
    // let sql = `select r.*,rd.played_duration,rd.is_like,rd.is_view,(SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel',LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl from reels r
    // LEFT JOIN reels_details rd ON r.id=rd.reel_id where rd.user_id=${userId} order by r.createddate desc`;

    let sql = `Update video_comments set is_active=1  where video_id= ${videoId} and is_active=1 order by createddate desc`;

    let sql1 = `select * from comment_details where user_id = ${userId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((comment) => {
              result[1].forEach((data) => {
                if (data.comment_id == comment.id) {
                  comment.is_unlike = data.is_unlike ? data.is_unlike : 0;
                  comment.is_like = data.is_like ? data.is_like : 0;
                  comment.is_react = data.is_react ? data.is_react : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Comment fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Comment not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getWatchingLaterVideo = (userId) =>
  new Promise((resolve, reject) => {
    //let sql = `select distinct(r.id) as distinctId,r.*,rd.played_duration,rd.is_like, rd.is_view from reels r left JOIN  reels_details rd ON r.id=rd.reel_id where r.is_shot=0 and rd.is_watch_later=1 and r.createdby = ${userId}  order by r.createddate desc`;

    let sql = ` select r.*,rd.is_like, rd.is_view, 0 as is_unlike,0 as channel_follow, (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id 
       left JOIN channels c ON r.video_channel=c.id
       where r.is_shot=0 and rd.is_watch_later=1 and rd.user_id=${userId} order by r.createddate desc`;

    let sql1 = `select channel_id as channelId,is_follow as isFollow from channel_details where user_id=${userId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getMyChannelByUserID = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `
  SELECT 
    c.id as channelId,
    c.name as channelName,
    c.description,
    c.channel_url as channelUrl,
    c.profile_image as profileImage,
    c.profile_cover_image as profileCoverImage,
    c.total_followers as totalSubscribers,
    c.total_videos as totalVideos,
    c.total_shorts as totalShorts,
    c.createdby as createdBy,
    c.updatedby as updatedBy,
    c.createddate,
    c.updateddate,
    c.total_ads_display,
    c.total_ads_like,
    c.total_ads_view,
    c.total_earnings,
    (SELECT COUNT(*) FROM reels r WHERE r.video_channel = c.id AND r.createdby = ${userId}) as totalReels
  FROM 
    channels c
  WHERE 
    c.createdby = ${userId}
  ORDER BY 
    c.createddate DESC`;
    console.log("sql-->", sql);
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          console.log("result", result);
          result.forEach((element) => {
            delete element.distinctId;
          });
          resolve({
            status: 200,
            message: "Channel details fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Channel not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getPopularVideo = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 and r.createdby=${userId} order by r.total_likes desc, r.total_views desc`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=${userId}`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getPopularShort = (userId, videoType) =>
  new Promise((resolve, reject) => {
    let sql = `select r.*,rd.is_like, rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=${videoType} and r.createdby=${userId} order by r.createddate desc`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=${userId}`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getRecentlyUploaded = (userId) =>
  new Promise((resolve, reject) => {
    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1 and r.is_shot=0 order by r.createddate desc, r.updateddate desc`;
    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 and r.createdby=${userId} order by r.createdDate desc`;

    let sql1 = `select is_follow as isFollow, channel_id as channelId from channel_details where user_id=${userId}`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAllRecentlyUploaded = (page) =>
  new Promise((resolve, reject) => {
    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1 and r.is_shot=0 order by r.createddate desc, r.updateddate desc`;

    let offset = (page - 1) * 5;

    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id 
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 order by r.createdDate desc limit 5 OFFSET ${offset}  `;

    let sql1 = `select is_follow as isFollow, channel_id as channelId from channel_details where user_id=0`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let searchfuntube = (data) =>
  new Promise((resolve, reject) => {
    let offset = (data.page - 1) * 5;

    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id 
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 AND 
    (r.name like '%${data.searchname}%' OR r.video_desciption like '%${data.searchname}%')
    order by r.createdDate desc limit 5 OFFSET ${offset}  `;

    let sql1 = `select is_follow as isFollow, channel_id as channelId from channel_details where user_id=0`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getRecentlyUploadedShort = (userId) =>
  new Promise((resolve, reject) => {
    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1 and r.is_shot=1 order by r.createddate desc, r.updateddate desc`;
    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=1 and r.createdby=${userId} order by r.createdDate desc`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=${userId}`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAllRecentlyUploadedShort = (page) =>
  new Promise((resolve, reject) => {
    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1 and r.is_shot=1 order by r.createddate desc, r.updateddate desc`;
    let offset = (page - 1) * 5;
    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id 
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=1 order by r.createdDate desc limit 5 OFFSET ${offset}`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=0`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getListOfFollowedChannelByUser = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select c.* from channels c
    JOIN channel_details cd ON c.id=cd.channel_id where cd.is_follow = 1 and cd.user_id = ${userId} order by c.createddate desc`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "List of Channel followed By User fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Recently list of followed channel not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getTrendingVideosOfAllChannel = (userId) =>
  new Promise((resolve, reject) => {
    // let sql = `select r.* from reels r
    // JOIN reels_details rd ON r.id=rd.reel_id
    // JOIN channel_details cd ON r.video_channel=cd.channel_id
    // where rd.user_id = ${userId} and cd.is_follow = 1 order by r.total_followers , r.total_likes desc`;

    // need video category name, advertise url, islike,isView,channel name

    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 order by c.total_followers , c.total_likes desc`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=${userId}`;

    //let sql = `select c.id as channelId,r.* from reels r RIGHT JOIN channels c ON r.video_channel= c.id where r.createdby = ${userId}  and r.is_active=1  order by r.total_likes desc, r.total_views desc`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getWeekVideoAllChannelFollowedByUser = (userId) =>
  new Promise((resolve, reject) => {
    // let sql = `select r.* from reels r
    // JOIN reels_details rd ON r.id=rd.reel_id
    // JOIN channel_details cd ON r.video_channel=cd.channel_id
    // where rd.user_id = ${userId} and YEARWEEK(r.createddate, 1) = YEARWEEK(CURDATE(), 1) order by r.total_followers , r.total_likes desc`;
    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
    (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
    (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
       c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
       c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
       from reels r  
       left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
       left JOIN channels c ON r.video_channel=c.id 
    where r.is_shot=0 and YEARWEEK(r.createddate, 1) = YEARWEEK(CURDATE(), 1) order by c.total_followers , c.total_likes desc`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=${userId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getMonthVideoAllChannelFollowedByUser = (userId) =>
  new Promise((resolve, reject) => {
    // let sql = `select r.* from reels r
    // JOIN reels_details rd ON r.id=rd.reel_id
    // JOIN channel_details cd ON r.video_channel=cd.channel_id
    // where rd.user_id = ${userId} and YEARWEEK(r.createddate, 1) != YEARWEEK(CURDATE(), 1) order by r.total_followers , r.total_likes desc`;

    let sql = `select r.*,rd.is_like,rd.is_view, 0 as channel_follow,
   (SELECT ad_upload_filename FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adUrl,
   (SELECT id FROM admodels where FIND_IN_SET('adtipsreel', LOWER(ad_place_app)) or FIND_IN_SET('all',LOWER(ad_place_app)) and is_active=1 ORDER BY RAND() LIMIT 1) as adId, 
      c.total_likes as total_channel_likes, c.total_followers as total_channel_followers, c.total_videos as total_channel_video, c.name as channelName, c.profile_image as channel_profile,
      c.id as channedlId, c.total_followers as total_followers, c.profile_cover_image as channelProfileCover, c.total_shorts as totalChannelShots, c.total_views as totalChannelViews, c.id as channelId
      from reels r  
      left JOIN reels_details rd ON r.id=rd.reel_id and rd.user_id=${userId}
      left JOIN channels c ON r.video_channel=c.id 
   where r.is_shot=0 and YEARWEEK(r.createddate, 1) != YEARWEEK(CURDATE(), 1) order by c.total_followers , c.total_likes desc`;

    let sql1 = `select is_follow as isFollow,channel_id as channelId from channel_details where user_id=${userId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0 &&
            result[1][0].length != 0
          ) {
            result[0].forEach((reelsData) => {
              result[1].forEach((data) => {
                reelsData.is_unlike =
                  reelsData.is_like == 1 ? 0 : reelsData.is_like;
                if (data.channelId == reelsData.video_channel) {
                  reelsData.channel_follow = data.isFollow ? data.isFollow : 0;
                }
              });
            });
          }
          resolve({
            status: 200,
            message: "Video fetch successfully.",
            data: result[0],
          });
        } else {
          resolve({
            status: 200,
            message: "Video not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getChannelByID = (id, userId) =>
  new Promise((resolve, reject) => {
    let sql = `select id as channelId,name as channelName,description,channel_url as channelUrl,0 as is_like, 0 as is_view,
    (select is_follow as isFollow from channel_details where user_id=${userId} and channel_id=${id}) as channel_follow,
    profile_image as profileImage, profile_cover_image as profileCoverImage,total_subscribers as totalSubscribers,
    total_videos as totalVideos,total_shorts as totalShorts, createdby as createdBy,updatedby as updatedBy,
    createddate,updateddate from channels where id = ${id} order by createddate desc`;

    console.log("sqlll", sql);
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Channel detalis fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Channel not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let updateChannel = (channelModel) =>
  new Promise((resolve, reject) => {
    let sql = `update channels set `;
    if (channelModel.channelName) sql += `name='${channelModel.channelName}',`;
    if (channelModel.channelDescription)
      sql += ` description='${channelModel.channelDescription}',`;
    if (channelModel.profileImageURL)
      sql += ` profile_image='${channelModel.profileImageURL}',`;
    if (channelModel.coverImageURL)
      sql += ` profile_cover_image='${channelModel.coverImageURL}',`;

    sql += ` updateddate=NOW(),`;
    if (sql !== "") sql = sql.substring(0, sql.length - 1);
    sql += `  where id=${channelModel.id}`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Channel detalis Updated.",
            data: [channelModel],
          });
        } else {
          resolve({
            status: 200,
            message: "Channel not updated.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveVideoComment = (commentModel) =>
  new Promise((resolve, reject) => {
    let commentURL = commentModel.commentURL ? commentModel.commentURL : null;
    let sql = `INSERT INTO video_comments(comment,video_id,is_active,createdby,createddate,commentator_name,commentator_image)
    values ('${commentModel.comment}',${commentModel.videoId},1,${commentModel.createdBy},Now(),"${commentModel.commentatorName}","${commentModel.commentatorImage}");`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        commentModel.id = result.insertId;
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Comment Added",
            data: [commentModel],
          });
        } else {
          reject({
            status: 400,
            message: "comment not save.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let saveVideoCommentLike = (reelsData) =>
  new Promise((resolve, reject) => {
    let sql = "",
      like = 0,
      unlike = 0;
    if (reelsData.isLike) like = reelsData.isLike === "1" ? 1 : 0;
    if (reelsData.isLike) unlike = reelsData.isLike === "0" ? 1 : 0;

    if (reelsData.isReact)
      sql += `INSERT INTO comment_details (comment_id,user_id,is_react,react_value,is_active,createdby,createddate) VALUES(${reelsData.commentId},${reelsData.userId},${reelsData.isReact},'${reelsData.reactValue}',1,${reelsData.createdby},NOW()) ON DUPLICATE KEY UPDATE`;
    if (reelsData.isLike)
      sql += `INSERT INTO comment_details (comment_id,user_id,is_like,is_unlike,is_active,createdby,createddate) VALUES(${reelsData.commentId},${reelsData.userId},${like},${unlike},1,${reelsData.createdby},NOW()) ON DUPLICATE KEY UPDATE`;
    //if (reelsData.isView) sql += `INSERT INTO comment_details (reel_id,user_id,is_view,createdby,createddate) VALUES(${reelsData.reelId},${reelsData.userId},'${reelsData.isView}',${reelsData.createdby},NOW()) ON DUPLICATE KEY UPDATE`;
    //if (reelsData.isWatchLater) sql += `INSERT INTO reels_details (reel_id,user_id,is_watch_later,createdby,createddate) VALUES(${reelsData.reelId},${reelsData.userId},'${reelsData.isWatchLater}',${reelsData.createdby},NOW()) ON DUPLICATE KEY UPDATE`;

    if (reelsData.isReact) {
      sql += ` react_value = '${
        reelsData.reactValue ? reelsData.reactValue : null
      }',is_react=${reelsData.isReact}`;
      if (reelsData.isReact === "1")
        dbQuery.queryRunner(
          `update video_comments set total_comment_react = total_comment_react + 1 where id=${reelsData.commentId};`
        );
      if (reelsData.isReact === "0")
        dbQuery.queryRunner(
          `update video_comments set total_comment_react = total_comment_react - 1 where id=${reelsData.commentId};`
        );
    }
    let lastWord = sql.split(" ").pop();

    if (lastWord && lastWord != "UPDATE" && reelsData.isReact) sql += ` ,`;
    lastWord = sql.split(" ").pop();

    if (reelsData.isLike) {
      sql += ` is_like = ${like} , is_unlike = ${unlike}`;
      lastWord = sql.split(" ").pop();

      if (reelsData.isLike === "1")
        dbQuery.queryRunner(
          `update video_comments set total_comment_like = total_comment_like + 1 where id=${reelsData.commentId};`
        );
      if (reelsData.isLike === "0")
        dbQuery.queryRunner(
          `update video_comments set total_comment_like = total_comment_like - 1 where id=${reelsData.commentId};`
        );
    }

    if (lastWord && lastWord != "UPDATE" && reelsData.isLike) sql += ` ,`;
    lastWord = sql.split(" ").pop();
    sql += ` updateddate = NOW();`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Video data save successfully.",
            data: [reelsData],
          });
        } else {
          reject({
            status: 400,
            message: "Video not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let createPlayList = (playListModel) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO playlist (name,cover_photo,channel_id,is_active,createdby,createddate)
    VALUE ('${playListModel.name}','${
      playListModel.coverImageURL ? playListModel.coverImageURL : null
    }',${playListModel.channelId},1,${
      playListModel.createdBy ? playListModel.createdBy : null
    },NOW())`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Playlist detalis saved.",
            data: [playListModel],
          });
        } else {
          resolve({
            status: 200,
            message: "Playlist not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getPlayListByChannelById = (channelId) =>
  new Promise((resolve, reject) => {
    let sql = `select r.*,c.name as channelName,p.name as playListName,cover_photo as coverPhoto from playlist p JOIN channels c ON c.id=p.channel_id JOIN reels r ON c.id=r.video_channel where p.is_active =1 and p.id=${channelId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch playlist successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Playlist not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getChildsUpdated = (current, allItems) => {
  let childs = allItems.filter((c) => c.parent_comment_id == current.id);
  for (let index = 0; index < childs.length; index++) {
    const child = childs[index];
    child.Children = getChildsUpdated(child, allItems);
  }
  current.Children = childs;
  return childs;
};

module.exports = {
  generatePresignedUrl: (data) => {
    return new Promise((resolve, reject) => {
      return generatePresignedUrl(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getUserVideoViewLikeDetails: (userId, page) => {
    return new Promise((resolve, reject) => {
      return getUserVideoViewLikeDetails(userId, page)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  saveVideoLike: (reelData) =>
    new Promise((resolve, reject) => {
      return saveVideoLike(reelData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  uploadShot: (shotModel) =>
    new Promise((resolve, reject) => {
      return uploadShot(shotModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  //getShortById
  getShortById: (userId, reelId) =>
    new Promise((resolve, reject) => {
      return getShortById(userId, reelId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  //editVideo
  editVideo: (video) =>
    new Promise((resolve, reject) => {
      return editVideo(video)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  ///getCommentOfVideo
  getCommentOfVideo: (videoId, page, limit) =>
    new Promise((resolve, reject) => {
      return getCommentOfVideo(videoId, page, limit)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deleteVideo: (videoId, userId) =>
    new Promise((resolve, reject) => {
      return deleteVideo(videoId, userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getTransactions: (data) =>
    new Promise((resolve, reject) => {
      return getTransactions(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveChannelWithdrawRequest: (data) =>
    new Promise((resolve, reject) => {
      return saveChannelWithdrawRequest(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  channelShortsVideos: (userId) => {
    return new Promise((resolve, reject) => {
      return channelShortsVideos(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  getShots: (userId) =>
    new Promise((resolve, reject) => {
      return getShots(userId)
        .then((result) => {
          if (result && result.status === 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getPublicShots: () =>
  new Promise((resolve, reject) => {
    return getPublicShots()
      .then((result) => {
        if (result && result.status === 200) {
          resolve(result);
        } else {
          reject(result);
        }
      })
      .catch((err) => {
        reject(err);
      });
  }),  

  getVideoCatagory: () =>
    new Promise((resolve, reject) => {
      return getVideoCatagory()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveVideoDetails: (shotModel) =>
    new Promise((resolve, reject) => {
      return saveVideoDetails(shotModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveChannelDetails: (channelModel) =>
    new Promise((resolve, reject) => {
      return saveChannelDetails(channelModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  ///saveChannelFollowers
  saveChannelFollowers: (data) =>
    new Promise((resolve, reject) => {
      return saveChannelFollowers(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveMyChannel: (channelModel) =>
    new Promise((resolve, reject) => {
      return saveMyChannel(channelModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getWatchingVideo: (userId) =>
    new Promise((resolve, reject) => {
      return getWatchingVideo(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAllVideos: (userId, categoryId, offset) =>
    new Promise((resolve, reject) => {
      return getAllVideos(userId, categoryId, offset)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getPublicVideos: (categoryId, offset) =>
    new Promise((resolve, reject) => {
      return getPublicVideos(categoryId, offset)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),    

  getVideoDetails: (userId, videoId) =>
    new Promise((resolve, reject) => {
      return getVideoDetails(userId, videoId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getVideoByChannel: (userId, channedlId, videoType) =>
    new Promise((resolve, reject) => {
      return getVideoByChannel(userId, channedlId, videoType)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getcommentsofvideos: (userId, videoId) =>
    new Promise((resolve, reject) => {
      let portfolio = [];
      return getcommentsofvideos(userId, videoId)
        .then((result) => {
          if (result && result.status == 200) {
            if (result.data && result.data.length != 0) {
              portfolio = result.data.filter((x) => x.parent_comment_id === 0);
              for (let index = 0; index < portfolio.length; index++) {
                const element = portfolio[index];
                getChildsUpdated(element, result.data);
              }
              result.data = portfolio;
              resolve(result);
            } else {
              resolve(result);
            }
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deletecommentbyid: (commentId) =>
    new Promise((resolve, reject) => {
      return deletecommentbyid(commentId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getWatchingLaterVideo: (userId) =>
    new Promise((resolve, reject) => {
      return getWatchingLaterVideo(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getMyChannelByUserID: (userId) =>
    new Promise((resolve, reject) => {
      return getMyChannelByUserID(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getPopularVideo: (userId) =>
    new Promise((resolve, reject) => {
      return getPopularVideo(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getPopularShort: (userId, videoType) =>
    new Promise((resolve, reject) => {
      return getPopularShort(userId, videoType)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getRecentlyUploaded: (userId) =>
    new Promise((resolve, reject) => {
      return getRecentlyUploaded(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAllRecentlyUploaded: (page) =>
    new Promise((resolve, reject) => {
      return getAllRecentlyUploaded(page)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  searchfuntube: (data) =>
    new Promise((resolve, reject) => {
      return searchfuntube(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getRecentlyUploadedShort: (userId) =>
    new Promise((resolve, reject) => {
      return getRecentlyUploadedShort(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAllRecentlyUploadedShort: (page) =>
    new Promise((resolve, reject) => {
      return getAllRecentlyUploadedShort(page)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getListOfFollowedChannelByUser: (userId) =>
    new Promise((resolve, reject) => {
      return getListOfFollowedChannelByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getTrendingVideosOfAllChannel: (userId) =>
    new Promise((resolve, reject) => {
      return getTrendingVideosOfAllChannel(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getWeekVideoAllChannelFollowedByUser: (userId) =>
    new Promise((resolve, reject) => {
      return getWeekVideoAllChannelFollowedByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getMonthVideoAllChannelFollowedByUser: (userId) =>
    new Promise((resolve, reject) => {
      return getMonthVideoAllChannelFollowedByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getChannelByID: (id, userid) =>
    new Promise((resolve, reject) => {
      return getChannelByID(id, userid)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  updateChannel: (channelModel) =>
    new Promise((resolve, reject) => {
      return updateChannel(channelModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  createPlayList: (playlistModel) =>
    new Promise((resolve, reject) => {
      return createPlayList(playlistModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getPlayListByChannelById: (channelId) =>
    new Promise((resolve, reject) => {
      return getPlayListByChannelById(channelId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveVideoComment: (commentModel) =>
    new Promise((resolve, reject) => {
      return saveVideoComment(commentModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveVideoCommentLike: (commentModel) =>
    new Promise((resolve, reject) => {
      return saveVideoCommentLike(commentModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  watchPaidVideo: async ({ video_id, viewer_id }) => {
    try {
      console.log(`watchPaidVideo - video_id: ${video_id}, viewer_id: ${viewer_id}`);

      const reelId = parseInt(video_id);
      const videoQuery = `
        SELECT 
          r.id, 
          r.createdby AS uploader_id, 
          r.name AS title, 
          r.promotional_price AS price_per_view, 
          r.is_paid_promotional, 
          r.is_active,
          r.video_link
        FROM reels r
        WHERE r.id = ? AND r.is_active = 1
        LIMIT 1
      `;
      const video = await dbQuery.queryRunner(videoQuery, [reelId]);

      console.log(`watchPaidVideo - Query Result: ${JSON.stringify(video)}`);

      if (!video.length) {
        return { statusCode: 404, message: "Video not found or inactive" };
      }

      const { id: reel_id, uploader_id, title, price_per_view, is_paid_promotional, video_link } = video[0];

      if (is_paid_promotional !== 1 || !price_per_view) {
        return { statusCode: 400, message: "Video is not a paid video" };
      }

      if (viewer_id === uploader_id) {
        return { statusCode: 403, message: "Cannot watch your own paid video" };
      }

      const viewQuery = `
        SELECT id FROM video_views 
        WHERE reel_id = ? AND viewer_id = ?
        LIMIT 1
      `;
      const existingView = await dbQuery.queryRunner(viewQuery, [reel_id, viewer_id]);

      console.log(`watchPaidVideo - Existing View: ${JSON.stringify(existingView)}`);

      if (existingView.length) {
        return {
          statusCode: 200,
          message: "Video already purchased, access granted",
          data: { video_link }
        };
      }

      // Fetch the latest wallet balance for the viewer
      const viewerWalletQuery = `
        SELECT id, totalBalance 
        FROM wallet 
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const viewerWallet = await dbQuery.queryRunner(viewerWalletQuery, [viewer_id]);

      console.log(`watchPaidVideo - Viewer Wallet: ${JSON.stringify(viewerWallet)}`);

      if (!viewerWallet.length || viewerWallet[0].totalBalance < price_per_view) {
        return {
          statusCode: 200,
          status: false,
          message: "not enough wallet balance to watch video",
          data: {}
        };
      }

      const viewerWalletId = viewerWallet[0].id;

      // Fetch the latest wallet entry for the uploader
      const uploaderWalletQuery = `
        SELECT id 
        FROM wallet 
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const uploaderWallet = await dbQuery.queryRunner(uploaderWalletQuery, [uploader_id]);

      console.log(`watchPaidVideo - Uploader Wallet: ${JSON.stringify(uploaderWallet)}`);

      if (!uploaderWallet.length) {
        return {
          statusCode: 500,
          message: "Uploader wallet not found",
          data: {}
        };
      }

      const uploaderWalletId = uploaderWallet[0].id;

      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

      await dbQuery.queryRunner('START TRANSACTION');

      try {
        // Update viewer's wallet balance (debit)
        await dbQuery.queryRunner(`
          UPDATE wallet 
          SET totalBalance = totalBalance - ?, updateddate = ?
          WHERE id = ? AND createdby = ?
        `, [price_per_view, currentTime, viewerWalletId, viewer_id]);

        // Update uploader's wallet balance (credit)
        await dbQuery.queryRunner(`
          UPDATE wallet 
          SET totalBalance = totalBalance + ?, updateddate = ?
          WHERE id = ? AND createdby = ?
        `, [price_per_view, currentTime, uploaderWalletId, uploader_id]);

        // Record the view (no transaction_id since we're not inserting into wallet)
        const viewInsertQuery = `
          INSERT INTO video_views (reel_id, viewer_id, view_time)
          VALUES (?, ?, ?)
        `;
        await dbQuery.queryRunner(viewInsertQuery, [reel_id, viewer_id, currentTime]);

        // Update total_views in reels
        const updateViewsQuery = `
          UPDATE reels 
          SET total_views = total_views + 1
          WHERE id = ?
        `;
        await dbQuery.queryRunner(updateViewsQuery, [reel_id]);

        await dbQuery.queryRunner('COMMIT');

        return {
          statusCode: 200,
          message: "Payment successful, video access granted",
          data: { video_link }
        };
      } catch (error) {
        await dbQuery.queryRunner('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error("Error in watchPaidVideo service:", error);
      return {
        statusCode: 500,
        message: "Internal Server Error",
        data: {}
      };
    }
  },

  watchNormalVideo: async ({ video_id, viewer_id }) => {
    try {
      console.log(`watchNormalVideo - video_id: ${video_id}, viewer_id: ${viewer_id}`);

      const reelId = parseInt(video_id);
      const videoQuery = `
        SELECT 
          r.id, 
          r.createdby AS uploader_id, 
          r.name AS title, 
          r.is_paid_promotional, 
          r.is_active,
          r.video_link
        FROM reels r
        WHERE r.id = ? AND r.is_active = 1
        LIMIT 1
      `;
      const video = await dbQuery.queryRunner(videoQuery, [reelId]);

      console.log(`watchNormalVideo - Query Result: ${JSON.stringify(video)}`);

      if (!video.length) {
        return {
          statusCode: 404,
          message: "Video not found or inactive",
          data: {}
        };
      }

      const { id: reel_id, uploader_id, title, is_paid_promotional, video_link } = video[0];

      if (is_paid_promotional === 1) {
        return {
          statusCode: 400,
          message: "Video is a paid video, use /viewPaidVideo instead",
          data: {}
        };
      }

      if (viewer_id === uploader_id) {
        return {
          statusCode: 200,
          message: "Access granted (uploader viewing own video)",
          data: { video_link }
        };
      }

      const viewQuery = `
        SELECT id FROM video_views 
        WHERE reel_id = ? AND viewer_id = ?
        LIMIT 1
      `;
      const existingView = await dbQuery.queryRunner(viewQuery, [reel_id, viewer_id]);

      console.log(`watchNormalVideo - Existing View: ${JSON.stringify(existingView)}`);

      if (existingView.length) {
        return {
          statusCode: 200,
          message: "Access granted (already viewed)",
          data: { video_link }
        };
      }

      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

      // Record the view (no transaction for normal videos)
      const viewInsertQuery = `
        INSERT INTO video_views (reel_id, viewer_id, view_time)
        VALUES (?, ?, ?)
      `;
      await dbQuery.queryRunner(viewInsertQuery, [reel_id, viewer_id, currentTime]);

      // Update total_views in reels
      const updateViewsQuery = `
        UPDATE reels 
        SET total_views = total_views + 1
        WHERE id = ?
      `;
      await dbQuery.queryRunner(updateViewsQuery, [reel_id]);

      return {
        statusCode: 200,
        message: "Access granted",
        data: { video_link }
      };
    } catch (error) {
      console.error("Error in watchNormalVideo service:", error);
      return {
        statusCode: 500,
        message: "Internal Server Error",
        data: {}
      };
    }
  },

  getChannelAnalytics: async (channelId) => {
    try {
      const channelQuery = `
        SELECT 
          c.id, c.name, c.profile_image, c.createdby, c.total_followers, c.total_videos
        FROM channels c
        WHERE c.id = ?
      `;
      const channelData = await dbQuery.queryRunner(channelQuery, [channelId]);

      if (channelData.length === 0) {
        throw new Error("Channel not found");
      }

      const {
        id,
        name,
        profile_image,
        createdby,
        total_followers,
        total_videos,
      } = channelData[0];

      // Paid views (unique users who watched paid videos)
      const paidViewsQuery = `
        SELECT COALESCE(COUNT(DISTINCT vv.viewer_id), 0) AS total_paid_views
        FROM reels r
        LEFT JOIN video_views vv ON r.id = vv.reel_id
        WHERE r.createdby = ? AND r.is_paid_promotional = 1
      `;
      const paidViewsData = await dbQuery.queryRunner(paidViewsQuery, [createdby]);
      const total_paid_views = paidViewsData[0].total_paid_views;

      // Normal views (unique users who watched non-paid videos)
      const normalViewsQuery = `
        SELECT COALESCE(COUNT(DISTINCT vv.viewer_id), 0) AS total_normal_views
        FROM reels r
        LEFT JOIN video_views vv ON r.id = vv.reel_id
        WHERE r.createdby = ? AND (r.is_paid_promotional = 0 OR r.is_paid_promotional IS NULL)
      `;
      const normalViewsData = await dbQuery.queryRunner(normalViewsQuery, [createdby]);
      const total_normal_views = normalViewsData[0].total_normal_views;

      // Total views (paid + normal)
      const total_views = total_paid_views + total_normal_views;

      // Fetch the latest wallet balance for the user
      const balanceQuery = `
        SELECT COALESCE(totalBalance, 0) AS available_balance
        FROM wallet
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const balanceData = await dbQuery.queryRunner(balanceQuery, [createdby]);
      const available_balance = parseFloat(balanceData[0].available_balance).toFixed(2);

      // Since we're not tracking individual transactions, paid_video_earned and withdrawn are not applicable
      // Set them to 0 or remove them if not needed
      const paid_video_earned = "0.00";
      const total_withdrawn = "0.00";

      return {
        channel_name: name || "No Name Available",
        channel_image: profile_image || "default_image_url",
        channel_followers: total_followers,
        total_videos: total_videos,
        total_paid_views: total_paid_views,
        total_normal_views: total_normal_views,
        total_views: total_views,
        paid_video_earned: paid_video_earned,
        withdrawn: total_withdrawn,
        available_balance: available_balance,
      };
    } catch (error) {
      console.error("Error in Analytics Service:", error);
      throw new Error("Error fetching channel analytics");
    }
  },

  watchSubscriptionPaidVideo: async ({ video_id, viewer_id }) => {
    try {
      // Fetch video details
      const videoQuery = `
        SELECT r.id, r.createdby AS uploader_id, r.name AS title, r.promotional_price AS price_per_view, r.is_paid_promotional, r.is_active, r.video_link
        FROM reels r
        WHERE r.id = ? AND r.is_active = 1
        LIMIT 1
      `;
      const video = await dbQuery.queryRunner(videoQuery, [video_id]);
      if (!video.length) {
        return { statusCode: 404, message: "Video not found or inactive" };
      }
      const { id: reel_id, uploader_id, price_per_view, is_paid_promotional, video_link } = video[0];
      if (is_paid_promotional !== 1 || !price_per_view) {
        return { statusCode: 400, message: "Video is not a paid video" };
      }
      if (viewer_id === uploader_id) {
        return { statusCode: 403, message: "Cannot watch your own paid video" };
      }

      // --- NEW: Check uploader's content creator subscription ---
      const subscriptionQuery = `
        SELECT status, current_end_at
        FROM content_creator_subscriptions
        WHERE user_id = ? AND status = 'active'
        ORDER BY created_at DESC
        LIMIT 1
      `;
      const subscription = await dbQuery.queryRunner(subscriptionQuery, [uploader_id]);
      if (!subscription.length || !subscription[0].current_end_at || new Date(subscription[0].current_end_at) < new Date()) {
        return { statusCode: 403, message: "Uploader does not have an active content creator subscription" };
      }

      // --- The rest is same as watchPaidVideo ---
      const viewQuery = `
        SELECT id FROM video_views 
        WHERE reel_id = ? AND viewer_id = ?
        LIMIT 1
      `;
      const existingView = await dbQuery.queryRunner(viewQuery, [reel_id, viewer_id]);
      if (existingView.length) {
        return { statusCode: 200, message: "Video already purchased, access granted", data: { video_link } };
      }

      // Fetch the latest wallet balance for the viewer
      const viewerWalletQuery = `
        SELECT id, totalBalance 
        FROM wallet 
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const viewerWallet = await dbQuery.queryRunner(viewerWalletQuery, [viewer_id]);
      if (!viewerWallet.length || viewerWallet[0].totalBalance < price_per_view) {
        return { statusCode: 200, status: false, message: "not enough wallet balance to watch video", data: {} };
      }
      const viewerWalletId = viewerWallet[0].id;

      // Fetch the latest wallet entry for the uploader
      const uploaderWalletQuery = `
        SELECT id 
        FROM wallet 
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const uploaderWallet = await dbQuery.queryRunner(uploaderWalletQuery, [uploader_id]);
      if (!uploaderWallet.length) {
        return { statusCode: 500, message: "Uploader wallet not found", data: {} };
      }
      const uploaderWalletId = uploaderWallet[0].id;

      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      await dbQuery.queryRunner('START TRANSACTION');
      try {
        // Update viewer's wallet balance (debit)
        await dbQuery.queryRunner(`
          UPDATE wallet 
          SET totalBalance = totalBalance - ?, updateddate = ?
          WHERE id = ? AND createdby = ?
        `, [price_per_view, currentTime, viewerWalletId, viewer_id]);
        // Update uploader's wallet balance (credit)
        await dbQuery.queryRunner(`
          UPDATE wallet 
          SET totalBalance = totalBalance + ?, updateddate = ?
          WHERE id = ? AND createdby = ?
        `, [price_per_view, currentTime, uploaderWalletId, uploader_id]);
        // Record the view
        const viewInsertQuery = `
          INSERT INTO video_views (reel_id, viewer_id, view_time)
          VALUES (?, ?, ?)
        `;
        await dbQuery.queryRunner(viewInsertQuery, [reel_id, viewer_id, currentTime]);
        // Update total_views in reels
        const updateViewsQuery = `
          UPDATE reels 
          SET total_views = total_views + 1
          WHERE id = ?
        `;
        await dbQuery.queryRunner(updateViewsQuery, [reel_id]);
        await dbQuery.queryRunner('COMMIT');
        return { statusCode: 200, message: "Payment successful, video access granted", data: { video_link } };
      } catch (error) {
        await dbQuery.queryRunner('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error("Error in watchSubscriptionPaidVideo service:", error);
      return { statusCode: 500, message: "Internal Server Error", data: {} };
    }
  },

  watchPaidVideoNoPremium: async ({ video_id, viewer_id }) => {
    try {
      // Fetch video details
      const videoQuery = `
        SELECT r.id, r.createdby AS uploader_id, r.name AS title, r.promotional_price AS price_per_view, r.is_paid_promotional, r.is_active, r.video_link
        FROM reels r
        WHERE r.id = ? AND r.is_active = 1
        LIMIT 1
      `;
      const video = await dbQuery.queryRunner(videoQuery, [video_id]);
      if (!video.length) {
        return { statusCode: 404, message: "Video not found or inactive" };
      }
      const { id: reel_id, uploader_id, price_per_view, is_paid_promotional, video_link } = video[0];
      if (is_paid_promotional !== 1 || !price_per_view) {
        return { statusCode: 400, message: "Video is not a paid video" };
      }
      if (viewer_id === uploader_id) {
        return { statusCode: 403, message: "Cannot watch your own paid video" };
      }

      // Check if already paid for this video
      const viewQuery = `
        SELECT id FROM video_views 
        WHERE reel_id = ? AND viewer_id = ?
        LIMIT 1
      `;
      const existingView = await dbQuery.queryRunner(viewQuery, [reel_id, viewer_id]);
      if (existingView.length) {
        return { statusCode: 200, message: "Video already purchased, access granted", data: { video_link } };
      }

      // Fetch the latest wallet balance for the viewer
      const viewerWalletQuery = `
        SELECT id, totalBalance 
        FROM wallet 
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const viewerWallet = await dbQuery.queryRunner(viewerWalletQuery, [viewer_id]);
      if (!viewerWallet.length || viewerWallet[0].totalBalance < price_per_view) {
        return { statusCode: 200, status: false, message: "not enough wallet balance to watch video", data: {} };
      }
      const viewerWalletId = viewerWallet[0].id;

      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      await dbQuery.queryRunner('START TRANSACTION');
      try {
        // Update viewer's wallet balance (debit)
        await dbQuery.queryRunner(`
          UPDATE wallet 
          SET totalBalance = totalBalance - ?, updateddate = ?
          WHERE id = ? AND createdby = ?
        `, [price_per_view, currentTime, viewerWalletId, viewer_id]);
        // Record the view and increment view count for subscription paid videos
        const viewInsertQuery = `
          INSERT INTO video_views (reel_id, viewer_id, view_time)
          VALUES (?, ?, ?)
        `;
        await dbQuery.queryRunner(viewInsertQuery, [reel_id, viewer_id, currentTime]);

        // Update total_views in reels for analytics tracking
        const updateViewsQuery = `
          UPDATE reels
          SET total_views = total_views + 1
          WHERE id = ?
        `;
        await dbQuery.queryRunner(updateViewsQuery, [reel_id]);

        await dbQuery.queryRunner('COMMIT');
        return { statusCode: 200, message: "Payment successful, video access granted", data: { video_link } };
      } catch (error) {
        await dbQuery.queryRunner('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error("Error in watchPaidVideoNoPremium service:", error);
      return { statusCode: 500, message: "Internal Server Error", data: {} };
    }
  },

  // Export new Stream-enabled upload function
  uploadShotWithStream,

  // Export Stream service for direct access
  CloudflareStreamService,
};
