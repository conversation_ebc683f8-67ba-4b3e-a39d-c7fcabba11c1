# 🚀 Cloudflare Stream Migration Guide

## Overview
This guide will help you migrate your entire video inventory to use **Cloudflare Stream URLs exclusively** while preserving all existing metadata (channel, description, likes, comments, etc.).

## 🎯 Goals
- ✅ **Stream-only playback** - Use Cloudflare Stream URLs for all videos
- ✅ **Automatic video playback** - Videos start playing automatically
- ✅ **Preserve all metadata** - Keep channels, descriptions, likes, comments
- ✅ **Intelligent mapping** - Match existing videos to Stream inventory
- ✅ **Zero data loss** - All existing data remains intact

## 📋 Prerequisites

### 1. Verify Cloudflare Stream Setup
```bash
# Test Stream API connection
cd adtipback
node -e "
const CloudflareStreamService = require('./services/CloudflareStreamService');
CloudflareStreamService.testConnection().then(result => {
  console.log('Stream API Test:', result);
  process.exit(result.success ? 0 : 1);
});
"
```

### 2. Check Database Schema
Ensure your database has the required Stream columns:
```sql
-- Verify columns exist
DESCRIBE reels;

-- Should show these columns:
-- stream_video_id VARCHAR(255)
-- stream_status ENUM('pending','uploading','ready','error')
-- adaptive_manifest_url VARCHAR(500)
-- stream_ready_at TIMESTAMP
```

## 🔄 Migration Process

### Step 1: Analyze Current State
```bash
cd adtipback/scripts
node analyzeVideoInventory.js
```

This will:
- Count videos in database vs Cloudflare Stream
- Identify videos needing Stream IDs
- Generate a detailed analysis report
- Save report to `migration_report_[timestamp].json`

### Step 2: Run Migration (Dry Run First)
```bash
cd adtipback/scripts
node runStreamMigration.js
```

Choose option **1** for dry run to see what would be changed without making actual updates.

### Step 3: Execute Live Migration
```bash
cd adtipback/scripts
node runStreamMigration.js
```

Choose option **2** for live run to execute actual database updates.

### Step 4: Verify Results
```bash
# Check updated video counts
cd adtipback/scripts
node analyzeVideoInventory.js
```

## 🔧 Technical Details

### Database Changes
The migration will update the `reels` table:
```sql
UPDATE reels SET 
  stream_video_id = '[CLOUDFLARE_STREAM_UID]',
  stream_status = 'ready',
  adaptive_manifest_url = 'https://customer-[CODE].cloudflarestream.com/[UID]/manifest/video.m3u8',
  stream_ready_at = NOW()
WHERE id = [VIDEO_ID];
```

### Video Matching Algorithm
Videos are matched using:
1. **Filename similarity** (40% weight)
2. **Metadata name matching** (30% weight)  
3. **Description similarity** (20% weight)
4. **Creation date proximity** (10% weight)

Minimum confidence threshold: **30%**

### Frontend Changes
- **VideoPlaybackService**: Prioritizes Stream URLs over R2 fallbacks
- **CloudflareStreamPlayer**: Enables autoplay by default
- **Enhanced error handling**: Better logging and fallback mechanisms

## 📊 Expected Results

### Before Migration
```
Database videos: 1000
Videos with Stream IDs: 50
Stream-ready videos: 45
Videos needing migration: 950
```

### After Migration
```
Database videos: 1000
Videos with Stream IDs: 900
Stream-ready videos: 895
Successfully migrated: 850
```

## 🎮 Frontend Behavior

### Stream-First Playback
1. **Primary**: Use Cloudflare Stream if `stream_status = 'ready'`
2. **Fallback**: Use R2 URL only if Stream unavailable
3. **Autoplay**: Videos start automatically when in view
4. **Error handling**: Graceful fallback with detailed logging

### Video Player Features
- ✅ **Adaptive streaming** via HLS manifests
- ✅ **Mobile optimization** through Cloudflare Stream
- ✅ **Automatic quality adjustment**
- ✅ **Fast loading** with CDN delivery
- ✅ **Bandwidth efficiency** with adaptive bitrates

## 🚨 Safety Features

### Dry Run Mode
- Test matching algorithm without database changes
- Preview all proposed updates
- Validate confidence scores
- Identify potential issues

### Batch Processing
- Process videos in configurable batches (default: 50)
- Transaction-based updates for data integrity
- Rollback on errors
- Progress tracking and logging

### Data Preservation
- **Zero data loss** - All existing metadata preserved
- **Reversible** - Original video_link URLs remain intact
- **Incremental** - Can be run multiple times safely
- **Selective** - Only updates videos with confident matches

## 🔍 Monitoring & Debugging

### Log Locations
- **Analysis logs**: Console output during analysis
- **Migration logs**: Console output during migration
- **Frontend logs**: Browser console for playback issues
- **Report files**: `migration_report_[timestamp].json`

### Key Metrics to Monitor
- Video playback success rate
- Stream URL load times
- Error rates in CloudflareStreamPlayer
- User engagement metrics

## 🆘 Troubleshooting

### Common Issues

#### 1. Stream API Connection Failed
```bash
# Check environment variables
echo $CLOUDFLARE_STREAM_API_TOKEN
echo $CLOUDFLARE_ACCOUNT_ID

# Verify token permissions in Cloudflare dashboard
```

#### 2. Low Match Confidence
- Review filename patterns in Stream vs database
- Check if metadata is properly set in Stream videos
- Adjust confidence threshold if needed

#### 3. Videos Not Playing
- Check browser console for CloudflareStreamPlayer errors
- Verify stream_status is 'ready' in database
- Test Stream URLs manually

### Recovery Options
If issues occur:
1. **Revert frontend changes** to use R2 fallbacks
2. **Re-run analysis** to identify problems
3. **Manual mapping** for high-value videos
4. **Gradual rollout** by video category

## 📞 Support
For issues during migration:
1. Check logs in console output
2. Review generated report files
3. Test individual video URLs manually
4. Verify Cloudflare Stream dashboard

---

## 🎉 Success Criteria
- ✅ All videos play automatically using Stream URLs
- ✅ Metadata (channels, likes, comments) preserved
- ✅ No broken video links
- ✅ Improved loading performance
- ✅ Mobile-optimized playback
