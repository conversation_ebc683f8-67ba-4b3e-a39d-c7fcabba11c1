Authorization header added to request for: /api/getshots/58422?page=1&limit=10
ApiService.ts:259 🚀 API REQUEST: {method: 'GET', url: '/api/getshots/58422?page=1&limit=10', baseURL: 'http://192.168.1.178:7082', fullURL: 'http://192.168.1.178:7082/api/getshots/58422?page=1&limit=10', headers: {…}, params: undefined, data: undefined, timeout: 60000, timestamp: '2025-07-25T15:43:21.858Z'}
CloudflareStreamPlayer.tsx:157 [CloudflareStreamPlayer] Video playback error: {error: {…}, target: 19186}error: {errorString: 'ExoPlaybackException: ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED', errorException: 'androidx.media3.exoplayer.ExoPlaybackException: Source error', errorCode: '23003', errorStackTrace: 'androidx.media3.exoplayer.ExoPlaybackException: Source error\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleIoException(ExoPlayerImplInternal.java:736)\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleMessage(ExoPlayerImplInternal.java:706)\n\tat android.os.Handler.dispatchMessage(Handler.java:103)\n\tat android.os.Looper.loopOnce(Looper.java:249)\n\tat android.os.Looper.loop(Looper.java:337)\n\tat android.os.HandlerThread.run(HandlerThread.java:85)\nCaused by: androidx.media3.exoplayer.source.UnrecognizedInputFormatException: None of the available extractors (FragmentedMp4Extractor, Mp4Extractor, FlvExtractor, FlacExtractor, WavExtractor, AmrExtractor, PsExtractor, OggExtractor, TsExtractor, MatroskaExtractor, AdtsExtractor, Ac3Extractor, Ac4Extractor, Mp3Extractor, AviExtractor, JpegExtractor, PngExtractor, WebpExtractor, BmpExtractor, HeifExtractor, AvifExtractor) could read the stream.{contentIsMalformed=false, dataType=1}\n\tat androidx.media3.exoplayer.source.BundledExtractorsAdapter.init(BundledExtractorsAdapter.java:108)\n\tat androidx.media3.exoplayer.source.ProgressiveMediaPeriod$ExtractingLoadable.load(ProgressiveMediaPeriod.java:1060)\n\tat androidx.media3.exoplayer.upstream.Loader$LoadTask.run(Loader.java:421)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)\n\tat java.lang.Thread.run(Thread.java:1012)\n'}target: 19186[[Prototype]]: Object
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
handleVideoError @ CloudflareStreamPlayer.tsx:157
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
EnhancedShortCard.tsx:471 [EnhancedShortCard] Video playback error: {error: {…}, target: 19186}error: {errorString: 'ExoPlaybackException: ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED', errorException: 'androidx.media3.exoplayer.ExoPlaybackException: Source error', errorCode: '23003', errorStackTrace: 'androidx.media3.exoplayer.ExoPlaybackException: Source error\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleIoException(ExoPlayerImplInternal.java:736)\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleMessage(ExoPlayerImplInternal.java:706)\n\tat android.os.Handler.dispatchMessage(Handler.java:103)\n\tat android.os.Looper.loopOnce(Looper.java:249)\n\tat android.os.Looper.loop(Looper.java:337)\n\tat android.os.HandlerThread.run(HandlerThread.java:85)\nCaused by: androidx.media3.exoplayer.source.UnrecognizedInputFormatException: None of the available extractors (FragmentedMp4Extractor, Mp4Extractor, FlvExtractor, FlacExtractor, WavExtractor, AmrExtractor, PsExtractor, OggExtractor, TsExtractor, MatroskaExtractor, AdtsExtractor, Ac3Extractor, Ac4Extractor, Mp3Extractor, AviExtractor, JpegExtractor, PngExtractor, WebpExtractor, BmpExtractor, HeifExtractor, AvifExtractor) could read the stream.{contentIsMalformed=false, dataType=1}\n\tat androidx.media3.exoplayer.source.BundledExtractorsAdapter.init(BundledExtractorsAdapter.java:108)\n\tat androidx.media3.exoplayer.source.ProgressiveMediaPeriod$ExtractingLoadable.load(ProgressiveMediaPeriod.java:1060)\n\tat androidx.media3.exoplayer.upstream.Loader$LoadTask.run(Loader.java:421)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)\n\tat java.lang.Thread.run(Thread.java:1012)\n'}target: 19186[[Prototype]]: Object
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
onError @ EnhancedShortCard.tsx:471
handleVideoError @ CloudflareStreamPlayer.tsx:159
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
RectangleAdComponent.tsx:52 Rectangle ad loaded successfully
CloudflareStreamPlayer.tsx:157 [CloudflareStreamPlayer] Video playback error: {error: {…}, target: 19298}error: {errorString: 'ExoPlaybackException: ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED', errorException: 'androidx.media3.exoplayer.ExoPlaybackException: Source error', errorCode: '23003', errorStackTrace: 'androidx.media3.exoplayer.ExoPlaybackException: Source error\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleIoException(ExoPlayerImplInternal.java:736)\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleMessage(ExoPlayerImplInternal.java:706)\n\tat android.os.Handler.dispatchMessage(Handler.java:103)\n\tat android.os.Looper.loopOnce(Looper.java:249)\n\tat android.os.Looper.loop(Looper.java:337)\n\tat android.os.HandlerThread.run(HandlerThread.java:85)\nCaused by: androidx.media3.exoplayer.source.UnrecognizedInputFormatException: None of the available extractors (FragmentedMp4Extractor, Mp4Extractor, FlvExtractor, FlacExtractor, WavExtractor, AmrExtractor, PsExtractor, OggExtractor, TsExtractor, MatroskaExtractor, AdtsExtractor, Ac3Extractor, Ac4Extractor, Mp3Extractor, AviExtractor, JpegExtractor, PngExtractor, WebpExtractor, BmpExtractor, HeifExtractor, AvifExtractor) could read the stream.{contentIsMalformed=false, dataType=1}\n\tat androidx.media3.exoplayer.source.BundledExtractorsAdapter.init(BundledExtractorsAdapter.java:108)\n\tat androidx.media3.exoplayer.source.ProgressiveMediaPeriod$ExtractingLoadable.load(ProgressiveMediaPeriod.java:1060)\n\tat androidx.media3.exoplayer.upstream.Loader$LoadTask.run(Loader.java:421)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)\n\tat java.lang.Thread.run(Thread.java:1012)\n'}target: 19298[[Prototype]]: Object
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
handleVideoError @ CloudflareStreamPlayer.tsx:157
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
EnhancedShortCard.tsx:471 [EnhancedShortCard] Video playback error: {error: {…}, target: 19298}error: {errorString: 'ExoPlaybackException: ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED', errorException: 'androidx.media3.exoplayer.ExoPlaybackException: Source error', errorCode: '23003', errorStackTrace: 'androidx.media3.exoplayer.ExoPlaybackException: Source error\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleIoException(ExoPlayerImplInternal.java:736)\n\tat androidx.media3.exoplayer.ExoPlayerImplInternal.handleMessage(ExoPlayerImplInternal.java:706)\n\tat android.os.Handler.dispatchMessage(Handler.java:103)\n\tat android.os.Looper.loopOnce(Looper.java:249)\n\tat android.os.Looper.loop(Looper.java:337)\n\tat android.os.HandlerThread.run(HandlerThread.java:85)\nCaused by: androidx.media3.exoplayer.source.UnrecognizedInputFormatException: None of the available extractors (FragmentedMp4Extractor, Mp4Extractor, FlvExtractor, FlacExtractor, WavExtractor, AmrExtractor, PsExtractor, OggExtractor, TsExtractor, MatroskaExtractor, AdtsExtractor, Ac3Extractor, Ac4Extractor, Mp3Extractor, AviExtractor, JpegExtractor, PngExtractor, WebpExtractor, BmpExtractor, HeifExtractor, AvifExtractor) could read the stream.{contentIsMalformed=false, dataType=1}\n\tat androidx.media3.exoplayer.source.BundledExtractorsAdapter.init(BundledExtractorsAdapter.java:108)\n\tat androidx.media3.exoplayer.source.ProgressiveMediaPeriod$ExtractingLoadable.load(ProgressiveMediaPeriod.java:1060)\n\tat androidx.media3.exoplayer.upstream.Loader$LoadTask.run(Loader.java:421)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)\n\tat java.lang.Thread.run(Thread.java:1012)\n'}target: 19298[[Prototype]]: Object
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
onError @ EnhancedShortCard.tsx:471
handleVideoError @ CloudflareStreamPlayer.tsx:159
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
CloudflareStreamPlayer.tsx:157 [CloudflareStreamPlayer] Video playback error: {error: {…}, target: 19448}
anonymous @ console.js:654
overrideMethod @ backend.js:17042

Show less
EnhancedShortCard.tsx:471 [EnhancedShortCard] Video playback error: {error: {…}, target: 19448}