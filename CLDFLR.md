[VideoPlaybackService] Stream playback support check: {videoId: '4730', streamVideoId: undefined, streamStatus: undefined, streamReadyAt: undefined, supports: false}
EnhancedShortCard.tsx:437 [EnhancedShortCard] Video debug info: {videoId: '4730', hasVideoLink: true, videoLinkValid: false, hasStreamId: false, streamIdValid: false, streamStatus: undefined, streamReady: false, hasStreamReadyAt: false, supportsStream: false, adaptiveManifestUrl: undefined}
VideoPlaybackService.ts:64 [VideoPlaybackService] Determining playback config: {videoId: '4730', streamVideoId: undefined, streamStatus: undefined, hasStreamVideo: undefined, useStream: undefined, preferStream: true, quality: 'auto', videoLink: 'https://94e2ffe1e7d5daf0d3de8d11c55dd2d6.r2.cloudflarestorage.com/adtip/videos/1752942281397_26k2lx.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=cee3aea0fa77a871fbc3d34a28897216%2F20250725%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250725T155849Z&X-Amz-Expires=3600&X-Amz-Signature=6bc9a8b14cc4a21d6755f591440d474e5f399ae22fafff67ee6b8b32cb13697c&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject'}
VideoPlaybackService.ts:79 [VideoPlaybackService] Using R2 fallback playback
VideoPlaybackService.ts:126 [VideoPlaybackService] Using R2 fallback: {videoId: '4730', videoUrl: 'https://94e2ffe1e7d5daf0d3de8d11c55dd2d6.r2.cloudflarestorage.com/adtip/videos/1752942281397_26k2lx.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=cee3aea0fa77a871fbc3d34a28897216%2F20250725%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250725T155849Z&X-Amz-Expires=3600&X-Amz-Signature=6bc9a8b14cc4a21d6755f591440d474e5f399ae22fafff67ee6b8b32cb13697c&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject', quality: 'auto'}
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://94e2ffe1e7d5daf0d3de8d11c55dd2d6.r2.cloudflarestorage.com/adtip/videos/1752942281397_26k2lx.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=cee3aea0fa77a871fbc3d34a28897216%2F20250725%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250725T155849Z&X-Amz-Expires=3600&X-Amz-Signature=6bc9a8b14cc4a21d6755f591440d474e5f399ae22fafff67ee6b8b32cb13697c&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject
CloudflareStreamPlayer.tsx:183 [CloudflareStreamPlayer] Video playback error: {error: {…}, target: 6084}
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
handleVideoError @ CloudflareStreamPlayer.tsx:183
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
EnhancedShortCard.tsx:477 [EnhancedShortCard] Video playback error: {error: {…}, videoId: '4729', streamVideoId: undefined, streamStatus: undefined, fallbackUrl: 'https://94e2ffe1e7d5daf0d3de8d11c55dd2d6.r2.cloudflarestorage.com/adtip/videos/1752941177538_4hrgnm.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=cee3aea0fa77a871fbc3d34a28897216%2F20250725%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250725T155849Z&X-Amz-Expires=3600&X-Amz-Signature=ae3fbf260369654072d40ce2d62b93ccac6d67f181e559fcd45e247b140c8056&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject'}
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
onError @ EnhancedShortCard.tsx:477
handleVideoError @ CloudflareStreamPlayer.tsx:185
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
CloudflareStreamPlayer.tsx:201 [CloudflareStreamPlayer] Using fallback video URL: https://94e2ffe1e7d5daf0d3de8d11c55dd2d6.r2.cloudflarestorage.com/adtip/videos/1752941177538_4hrgnm.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=cee3aea0fa77a871fbc3d34a28897216%2F20250725%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250725T155849Z&X-Amz-Expires=3600&X-Amz-Signature=ae3fbf260369654072d40ce2d62b93ccac6d67f181e559fcd45e247b140c8056&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject
CloudflareStreamPlayer.tsx:183 [CloudflareStreamPlayer] Video playback error: {error: {…}, target: 6196}
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:231
anonymous @ LogBox.js:80
handleVideoError @ CloudflareStreamPlayer.tsx:183
anonymous @ Video.tsx:492
executeDispatch @ ReactFabric-dev.js:599
executeDispatchesAndReleaseTopLevel @ ReactFabric-dev.js:1362
forEachAccumulated @ ReactFabric-dev.js:840
anonymous @ ReactFabric-dev.js:1399
batchedUpdatesImpl @ ReactFabric-dev.js:16520
batchedUpdates$1 @ ReactFabric-dev.js:1343
dispatchEvent @ ReactFabric-dev.js:1374
Show 14 more frames
Show less
EnhancedShortCard.tsx:477 [EnhancedShortCard] Video playback error: 